// web/src/routes/api/health/collector/+server.ts
// Centralized health data collector for all consumer-facing services

import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import { HealthMonitor } from '$lib/server/healthMonitor';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ fetch, url }) => {
  const startTime = performance.now();
  
  try {
    const healthMonitor = HealthMonitor.getInstance();
    
    // Get query parameters
    const service = url.searchParams.get('service');
    const includeDetails = url.searchParams.get('details') === 'true';
    
    if (service) {
      // Get health for specific service
      const healthData = await healthMonitor.getServiceHealth(service, fetch);
      const responseTime = Math.round(performance.now() - startTime);
      
      return json({
        service: healthData.service,
        status: healthData.status,
        responseTime: healthData.responseTime,
        ...(includeDetails && { details: healthData.details }),
        timestamp: healthData.timestamp,
        collectorResponseTime: responseTime,
      });
    } else {
      // Get health for all services
      const allHealthData = await healthMonitor.getAllServicesHealth(fetch);
      const responseTime = Math.round(performance.now() - startTime);
      
      // Calculate overall system status
      const services = Object.values(allHealthData);
      let overallStatus = 'operational';
      
      if (services.some(s => s.status === 'outage')) {
        overallStatus = 'outage';
      } else if (services.some(s => s.status === 'degraded')) {
        overallStatus = 'degraded';
      } else if (services.some(s => s.status === 'maintenance')) {
        overallStatus = 'maintenance';
      } else if (services.some(s => s.status === 'unknown')) {
        overallStatus = 'degraded'; // Treat unknown as degraded
      }
      
      // Calculate average response time
      const avgResponseTime = services.length > 0 
        ? Math.round(services.reduce((sum, s) => sum + s.responseTime, 0) / services.length)
        : 0;
      
      // Count services by status
      const statusCounts = services.reduce((counts, service) => {
        counts[service.status] = (counts[service.status] || 0) + 1;
        return counts;
      }, {} as Record<string, number>);
      
      const response = {
        overallStatus,
        totalServices: services.length,
        statusCounts,
        averageResponseTime: avgResponseTime,
        services: includeDetails 
          ? allHealthData 
          : Object.fromEntries(
              Object.entries(allHealthData).map(([key, value]) => [
                key, 
                {
                  service: value.service,
                  status: value.status,
                  responseTime: value.responseTime,
                  timestamp: value.timestamp,
                }
              ])
            ),
        collectorResponseTime: responseTime,
        timestamp: new Date().toISOString(),
      };
      
      logger.info(
        `Health collector: ${overallStatus} (${services.length} services, avg ${avgResponseTime}ms)`
      );
      
      return json(response);
    }
  } catch (error) {
    const responseTime = Math.round(performance.now() - startTime);
    logger.error('Health collector error:', error);
    
    return json(
      {
        error: 'Health collection failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        collectorResponseTime: responseTime,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

export const POST: RequestHandler = async ({ request, fetch }) => {
  try {
    const body = await request.json();
    const { services, forceRefresh } = body;
    
    const healthMonitor = HealthMonitor.getInstance();
    
    if (forceRefresh) {
      // Clear cache to force fresh health checks
      logger.info('Forcing health data refresh for all services');
    }
    
    let servicesToCheck = services;
    if (!servicesToCheck || !Array.isArray(servicesToCheck)) {
      servicesToCheck = ['matches', 'jobs', 'tracker', 'documents', 'automation', 'system', 'website'];
    }
    
    const healthData: Record<string, any> = {};
    const errors: string[] = [];
    
    // Check health for specified services
    await Promise.all(
      servicesToCheck.map(async (serviceName: string) => {
        try {
          healthData[serviceName] = await healthMonitor.getServiceHealth(serviceName, fetch);
        } catch (error) {
          const errorMessage = `Failed to check ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          logger.error(errorMessage);
          
          healthData[serviceName] = {
            service: serviceName,
            status: 'unknown',
            responseTime: 0,
            details: { error: errorMessage },
            timestamp: new Date().toISOString(),
          };
        }
      })
    );
    
    const response = {
      success: errors.length === 0,
      servicesChecked: servicesToCheck.length,
      healthData,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString(),
    };
    
    logger.info(`Health collection completed: ${servicesToCheck.length} services checked, ${errors.length} errors`);
    
    return json(response);
  } catch (error) {
    logger.error('Health collector POST error:', error);
    
    return json(
      {
        success: false,
        error: 'Health collection request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

// OPTIONS handler for CORS
export const OPTIONS: RequestHandler = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
};
